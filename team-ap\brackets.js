// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let currentTournament = null;
let teams = [];
let brackets = [];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadData();
});

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
}

// API helper function
async function apiCall(endpoint) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`API call failed: ${response.status} ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.error('API call error:', error);
        return null;
    }
}

// Load all data
async function loadData() {
    await loadCurrentTournament();
    if (currentTournament) {
        await loadTeams();
        await loadBrackets();
        displayTournamentInfo();
        displayBrackets();
    } else {
        await loadMostRecentTournament();
    }
}

// Load current live tournament
async function loadCurrentTournament() {
    currentTournament = await apiCall('/api/tournaments/live');
}

// Load most recent completed tournament if no live tournament
async function loadMostRecentTournament() {
    const completedTournaments = await apiCall('/api/tournaments/completed');
    if (completedTournaments && completedTournaments.length > 0) {
        currentTournament = completedTournaments.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        )[0];
        
        await loadTeams();
        await loadBrackets();
        displayTournamentInfo();
        displayBrackets();
    } else {
        displayNoTournament();
    }
}

// Load teams for current tournament
async function loadTeams() {
    if (currentTournament) {
        teams = await apiCall(`/api/tournaments/${currentTournament.id}/teams`) || [];
    }
}

// Load brackets for current tournament
async function loadBrackets() {
    if (currentTournament) {
        brackets = await apiCall(`/api/brackets/${currentTournament.id}`) || [];
        // Sort brackets by round
        brackets.sort((a, b) => a.round - b.round);
    }
}

// Display tournament information
function displayTournamentInfo() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    
    if (!currentTournament) {
        displayNoTournament();
        return;
    }
    
    const statusText = currentTournament.status === 'live' ? 'LIVE NOW' : 
                      currentTournament.status === 'upcoming' ? 'UPCOMING' : 'COMPLETED';
    
    tournamentInfoElement.innerHTML = `
        <div class="tournament-info-content">
            <h2 class="tournament-name">${currentTournament.name}</h2>
            <div class="tournament-details">
                <span class="tournament-type">${currentTournament.type}</span>
                <span class="tournament-status ${currentTournament.status}">${statusText}</span>
                ${currentTournament.prize_pool ? `<span class="tournament-prize">₹${currentTournament.prize_pool.toLocaleString()}</span>` : ''}
            </div>
        </div>
    `;
}

// Display brackets
function displayBrackets() {
    const bracketsContainer = document.getElementById('bracketsContainer');
    
    if (!brackets || brackets.length === 0) {
        bracketsContainer.innerHTML = `
            <div class="no-brackets">
                <h2>No Brackets Available</h2>
                <p>Brackets will be displayed once the tournament structure is set up.</p>
            </div>
        `;
        return;
    }
    
    // Sort brackets by round number for proper progression display
    const sortedBrackets = brackets.sort((a, b) => a.round - b.round);

    bracketsContainer.innerHTML = `
        <div class="brackets-wrapper">
            ${sortedBrackets.map(bracket => {
                const roundName = getRoundDisplayName(bracket.round, bracket.round_name, sortedBrackets.length);
                return `
                    <div class="bracket-round">
                        <div class="round-header">
                            <h3 class="round-title">${roundName}</h3>
                            <span class="round-subtitle">Round ${bracket.round}</span>
                        </div>
                        <div class="matchups-container">
                            ${sortMatchupsByTime(bracket.matchups).map((matchup, index) => `
                                <div class="bracket-matchup-card">
                                    <div class="matchup-header">
                                        <span class="matchup-label">Match ${index + 1}</span>
                                        ${matchup.match_id ? `<span class="match-id">#${matchup.match_id}</span>` : ''}
                                    </div>
                                    ${matchup.start_time ? `
                                        <div class="matchup-time">
                                            <span class="time-label">Scheduled:</span>
                                            <span class="time-value">${formatBracketTime(matchup.start_time)}</span>
                                        </div>
                                    ` : ''}
                                    <div class="matchup-teams">
                                        <div class="bracket-team ${matchup.winner === matchup.teamA ? 'winner' : ''}">
                                            <div class="team-logo">${matchup.teamA ? getTeamById(matchup.teamA)?.initials || '??' : '??'}</div>
                                            <div class="team-details">
                                                <div class="team-name">${matchup.teamA ? getTeamById(matchup.teamA)?.name || 'Unknown Team' : 'TBD'}</div>
                                            </div>
                                            ${matchup.scoreA !== null && matchup.scoreA !== undefined ?
                                                `<div class="team-score">${matchup.scoreA}</div>` :
                                                '<div class="team-score">-</div>'}
                                        </div>
                                        <div class="vs-section">
                                            <div class="vs-text">VS</div>
                                        </div>
                                        <div class="bracket-team ${matchup.winner === matchup.teamB ? 'winner' : ''}">
                                            <div class="team-logo">${matchup.teamB ? getTeamById(matchup.teamB)?.initials || '??' : '??'}</div>
                                            <div class="team-details">
                                                <div class="team-name">${matchup.teamB ? getTeamById(matchup.teamB)?.name || 'Unknown Team' : 'TBD'}</div>
                                            </div>
                                            ${matchup.scoreB !== null && matchup.scoreB !== undefined ?
                                                `<div class="team-score">${matchup.scoreB}</div>` :
                                                '<div class="team-score">-</div>'}
                                        </div>
                                    </div>
                                    ${matchup.winner ? `
                                        <div class="matchup-result">
                                            <div class="winner-badge">
                                                🏆 <span class="winner-text">${getTeamById(matchup.winner)?.name || 'Unknown Team'}</span>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;
}

// Display no tournament message
function displayNoTournament() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    const bracketsContainer = document.getElementById('bracketsContainer');
    
    const noTournamentMessage = `
        <div class="no-tournament">
            <h2>No Tournament Currently Available</h2>
            <p>Check back soon for upcoming tournaments!</p>
        </div>
    `;
    
    if (tournamentInfoElement) {
        tournamentInfoElement.innerHTML = noTournamentMessage;
    }
    
    if (bracketsContainer) {
        bracketsContainer.innerHTML = `
            <div class="no-brackets">
                <h2>No Tournament is Live Currently</h2>
                <p>Brackets will be available when a tournament is active.</p>
            </div>
        `;
    }
}

// Helper function to get team by ID
function getTeamById(teamId) {
    return teams.find(team => team.id === teamId);
}

// Utility function to convert to IST timezone
function toISTDate(dateString) {
    const date = new Date(dateString);
    // Convert to IST (UTC+5:30)
    const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    return new Date(utc + istOffset);
}

// Sort matchups within a round by their scheduled start time
function sortMatchupsByTime(matchups) {
    return matchups.sort((a, b) => {
        // If both have start times, sort by start time
        if (a.start_time && b.start_time) {
            return new Date(a.start_time) - new Date(b.start_time);
        }
        // If only one has start time, prioritize it
        if (a.start_time && !b.start_time) return -1;
        if (!a.start_time && b.start_time) return 1;
        // If neither has start time, maintain original order
        return 0;
    });
}

// Format time for bracket display with IST timezone
function formatBracketTime(dateString) {
    const date = toISTDate(dateString);
    return date.toLocaleDateString('en-IN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Kolkata'
    });
}

// Helper function to get proper round display names
function getRoundDisplayName(roundNumber, customName, totalRounds) {
    // If there's a custom name, use it
    if (customName) {
        return customName;
    }

    // Generate standard tournament round names based on position
    const roundsFromEnd = totalRounds - roundNumber + 1;

    switch (roundsFromEnd) {
        case 1:
            return 'Finals';
        case 2:
            return 'Semifinals';
        case 3:
            return 'Quarterfinals';
        case 4:
            return 'Round of 16';
        case 5:
            return 'Round of 32';
        default:
            if (roundNumber === 1) {
                return 'First Round';
            }
            return `Round ${roundNumber}`;
    }
}
