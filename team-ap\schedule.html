<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schedule - Team AP</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>TEAM-<span class="accent">AP</span></h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">HOME</a>
                </li>
                <li class="nav-item">
                    <a href="schedule.html" class="nav-link active">SCHEDULE</a>
                </li>
                <li class="nav-item">
                    <a href="brackets.html" class="nav-link">BRACKETS</a>
                </li>
                <li class="nav-item">
                    <a href="history.html" class="nav-link">HISTORY</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1 class="section-title">MATCH SCHEDULE</h1>
            <p class="page-description">Stay updated with all upcoming and completed matches</p>
            <div id="tournamentInfo" class="tournament-info-card">
                <div class="loading">Loading tournament information...</div>
            </div>
        </div>
    </section>

    <!-- Schedule Content -->
    <section class="schedule-content">
        <div class="container">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <button class="filter-btn active" data-filter="all">ALL MATCHES</button>
                <button class="filter-btn" data-filter="upcoming">UPCOMING</button>
                <button class="filter-btn" data-filter="live">LIVE</button>
                <button class="filter-btn" data-filter="completed">COMPLETED</button>
            </div>

            <!-- Matches List -->
            <div id="matchesList" class="matches-table">
                <div class="loading">Loading matches...</div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Team-AP Valorant Tournaments. Created by shushie. Not affiliated with Riot Games.</p>
        </div>
    </footer>

    <script src="schedule.js"></script>
</body>
</html>
